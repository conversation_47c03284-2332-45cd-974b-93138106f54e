// Background script for JupiterEd to Microsoft To-Do extension
class JupiterEdToDoExtension {
  constructor() {
    this.accessToken = null;
    this.tokenExpiry = null;
    this.clientId = null;
    this.selectedListId = null;
    this.settings = {};
    this.init();
  }

  async init() {
    // Load settings from storage
    await this.loadSettings();

    // Set up message listeners
    chrome.runtime.onMessage.addListener((message, sender, sendResponse) => {
      this.handleMessage(message, sender, sendResponse);
      return true; // Keep message channel open for async response
    });

    // Set up installation handler
    chrome.runtime.onInstalled.addListener((details) => {
      if (details.reason === 'install') {
        chrome.tabs.create({ url: chrome.runtime.getURL('settings.html') });
      }
    });
  }

  async loadSettings() {
    try {
      const result = await chrome.storage.sync.get([
        'clientId',
        'selectedListId',
        'autoSync',
        'notificationsEnabled',
        'accessToken',
        'tokenExpiry'
      ]);

      this.clientId = result.clientId;
      this.selectedListId = result.selectedListId || 'tasks'; // Default to main tasks list
      this.settings = {
        autoSync: result.autoSync || false,
        notificationsEnabled: result.notificationsEnabled !== false
      };

      // Load stored token if valid
      if (result.accessToken && result.tokenExpiry && new Date(result.tokenExpiry) > new Date()) {
        this.accessToken = result.accessToken;
        this.tokenExpiry = new Date(result.tokenExpiry);
      }
    } catch (error) {
      console.error('Error loading settings:', error);
    }
  }

  async saveSettings() {
    try {
      await chrome.storage.sync.set({
        clientId: this.clientId,
        selectedListId: this.selectedListId,
        autoSync: this.settings.autoSync,
        notificationsEnabled: this.settings.notificationsEnabled,
        accessToken: this.accessToken,
        tokenExpiry: this.tokenExpiry?.toISOString()
      });
    } catch (error) {
      console.error('Error saving settings:', error);
    }
  }

  async handleMessage(message, sender, sendResponse) {
    try {
      switch (message.type) {
        case 'UPLOAD_ASSIGNMENTS':
          await this.uploadAssignments(message.assignments, sendResponse);
          break;
        case 'GET_AUTH_STATUS':
          sendResponse({ authenticated: !!this.accessToken, hasClientId: !!this.clientId });
          break;
        case 'AUTHENTICATE':
          await this.authenticate(sendResponse);
          break;
        case 'GET_TODO_LISTS':
          await this.getTodoLists(sendResponse);
          break;
        case 'UPDATE_SETTINGS':
          await this.updateSettings(message.settings, sendResponse);
          break;
        case 'LOGOUT':
          await this.logout(sendResponse);
          break;
        default:
          sendResponse({ error: 'Unknown message type' });
      }
    } catch (error) {
      console.error('Error handling message:', error);
      sendResponse({ error: error.message });
    }
  }

  async uploadAssignments(assignments, sendResponse) {
    console.log('🚀 Background: Starting upload process for', assignments.length, 'assignments');

    if (!this.clientId) {
      const error = 'Client ID not configured. Please set up the extension first.';
      console.error('❌ Background:', error);
      sendResponse({ error });
      return;
    }

    if (!this.accessToken || (this.tokenExpiry && new Date() >= this.tokenExpiry)) {
      console.log('🔐 Background: Authentication required');
      const authResult = await this.authenticate();
      if (!authResult.success) {
        const error = 'Authentication failed: ' + authResult.error;
        console.error('❌ Background:', error);
        sendResponse({ error });
        return;
      }
      console.log('✅ Background: Authentication successful');
    }

    const results = {
      success: 0,
      failed: 0,
      skipped: 0,
      errors: []
    };

    console.log('📊 Background: Processing assignments...');

    // Get existing tasks to check for duplicates
    let existingTasks = [];
    try {
      console.log('🔍 Fetching existing tasks for duplicate checking...');
      existingTasks = await this.getExistingTasks();
      console.log(`✅ Found ${existingTasks.length} existing tasks`);

      if (existingTasks.length > 0) {
        console.log('📋 Sample existing tasks:');
        existingTasks.slice(0, 3).forEach((task, index) => {
          console.log(`   ${index + 1}. "${task.title}" (due: ${task.dueDateTime?.dateTime || 'none'})`);
        });
        if (existingTasks.length > 3) {
          console.log(`   ... and ${existingTasks.length - 3} more`);
        }
      }
    } catch (error) {
      console.warn('⚠️  Failed to fetch existing tasks, proceeding without duplicate check:', error);
      console.warn('   This means all assignments will be treated as new tasks');
      // Continue without duplicate checking if fetching fails
    }

    console.log(`📝 Processing ${assignments.length} assignments...`);

    for (let i = 0; i < assignments.length; i++) {
      const assignment = assignments[i];
      try {
        const taskTitle = `${assignment.course}: ${assignment.title}`;
        console.log(`\n🔍 [${i+1}/${assignments.length}] Processing assignment:`, {
          title: taskTitle,
          dueDate: assignment.dueDate,
          course: assignment.course,
          source: assignment.source
        });

        // Check if task already exists (only if we successfully fetched existing tasks)
        let isDuplicate = false;
        if (existingTasks.length > 0) {
          console.log(`🔍 Checking against ${existingTasks.length} existing tasks...`);

          for (const task of existingTasks) {
            const titleMatch = task.title === taskTitle;
            const dateMatch = this.isSameDueDate(task.dueDateTime, assignment.dueDate);

            console.log(`   📋 Comparing with: "${task.title}"`);
            console.log(`      - Title match: ${titleMatch}`);
            console.log(`      - Date match: ${dateMatch} (existing: ${task.dueDateTime?.dateTime || 'none'}, new: ${assignment.dueDate || 'none'})`);

            if (titleMatch && dateMatch) {
              isDuplicate = true;
              console.log(`   ✅ Found duplicate!`);
              break;
            }
          }
        } else {
          console.log(`ℹ️  No existing tasks to check against (existingTasks.length = ${existingTasks.length})`);
        }

        if (isDuplicate) {
          console.log(`⏭️  Skipping duplicate task: ${taskTitle}`);
          results.skipped++;
          continue;
        }

        console.log(`🚀 Creating new task: ${taskTitle}`);
        const createdTask = await this.createTask(assignment);
        console.log(`✅ Task created successfully:`, createdTask.id);
        results.success++;
      } catch (error) {
        console.error(`❌ Error processing assignment "${assignment.title}":`, error);
        console.error(`   Error details:`, {
          message: error.message,
          stack: error.stack,
          assignment: assignment
        });
        results.failed++;
        results.errors.push(`${assignment.title}: ${error.message}`);
      }
    }

    // Enhanced reporting
    console.log('📊 Background: Upload results:', results);
    console.log(`✅ Success: ${results.success}`);
    console.log(`⏭️  Skipped: ${results.skipped}`);
    console.log(`❌ Failed: ${results.failed}`);

    if (results.errors.length > 0) {
      console.log('🚨 Errors encountered:');
      results.errors.forEach((error, index) => {
        console.log(`   ${index + 1}. ${error}`);
      });
    }

    if (this.settings.notificationsEnabled) {
      const message = results.failed > 0
        ? `${results.success} uploaded, ${results.failed} failed, ${results.skipped} skipped`
        : `${results.success} uploaded, ${results.skipped} skipped`;

      chrome.notifications.create({
        type: 'basic',
        iconUrl: 'icons/icon48.png',
        title: 'JupiterEd Sync Complete',
        message: message
      });
    }

    console.log('🏁 Background: Upload process completed');
    sendResponse(results);
  }

  async authenticate(sendResponse = null) {
    console.log('Authenticate called, current clientId:', this.clientId);

    // Try to reload settings if clientId is missing
    if (!this.clientId) {
      console.log('Client ID missing, reloading settings...');
      await this.loadSettings();
      console.log('After reload, clientId:', this.clientId);
    }

    if (!this.clientId) {
      const error = 'Client ID not configured. Please enter your Azure App Client ID in settings.';
      console.error(error);
      if (sendResponse) sendResponse({ success: false, error });
      return { success: false, error };
    }

    try {
      const result = await new Promise((resolve, reject) => {
        const redirectUri = chrome.identity.getRedirectURL();
        console.log('Using redirect URI:', redirectUri);

        const authUrl = `https://login.microsoftonline.com/common/oauth2/v2.0/authorize?` +
          `client_id=${this.clientId}&` +
          `response_type=token&` +
          `redirect_uri=${encodeURIComponent(redirectUri)}&` +
          `scope=${encodeURIComponent('https://graph.microsoft.com/Tasks.ReadWrite')}&` +
          `response_mode=fragment&` +
          `nonce=${Date.now()}`;

        console.log('Auth URL:', authUrl);

        chrome.identity.launchWebAuthFlow({
          url: authUrl,
          interactive: true
        }, (redirectUrl) => {
          if (chrome.runtime.lastError) {
            reject(new Error(chrome.runtime.lastError.message));
            return;
          }

          if (!redirectUrl) {
            reject(new Error('Authentication cancelled'));
            return;
          }

          try {
            const url = new URL(redirectUrl);
            console.log('Redirect URL:', redirectUrl);

            // Try to get token from hash fragment first (implicit flow)
            let params = new URLSearchParams(url.hash.substring(1));
            let accessToken = params.get('access_token');
            let expiresIn = params.get('expires_in');

            // If not in hash, try query parameters (authorization code flow)
            if (!accessToken) {
              params = new URLSearchParams(url.search);
              accessToken = params.get('access_token');
              expiresIn = params.get('expires_in');
            }

            // If still no token, check for authorization code
            if (!accessToken) {
              const code = params.get('code');
              if (code) {
                reject(new Error('Authorization code flow not supported. Please use implicit flow.'));
                return;
              }
            }

            console.log('Parsed params:', Object.fromEntries(params));
            console.log('Access token found:', !!accessToken);

            if (!accessToken) {
              reject(new Error('No access token received. Check your Azure app configuration and ensure implicit flow is enabled.'));
              return;
            }

            resolve({ accessToken, expiresIn });
          } catch (error) {
            console.error('Error parsing redirect URL:', error);
            reject(error);
          }
        });
      });

      this.accessToken = result.accessToken;
      this.tokenExpiry = new Date(Date.now() + (parseInt(result.expiresIn) * 1000));
      await this.saveSettings();

      if (sendResponse) sendResponse({ success: true });
      return { success: true };
    } catch (error) {
      console.error('Authentication error:', error);
      if (sendResponse) sendResponse({ success: false, error: error.message });
      return { success: false, error: error.message };
    }
  }

  async getTodoLists(sendResponse) {
    if (!this.accessToken) {
      sendResponse({ error: 'Not authenticated' });
      return;
    }

    try {
      const response = await fetch('https://graph.microsoft.com/v1.0/me/todo/lists', {
        headers: {
          'Authorization': `Bearer ${this.accessToken}`,
          'Content-Type': 'application/json'
        }
      });

      if (!response.ok) {
        throw new Error(`HTTP ${response.status}: ${response.statusText}`);
      }

      const data = await response.json();
      sendResponse({ success: true, lists: data.value });
    } catch (error) {
      console.error('Error fetching todo lists:', error);
      sendResponse({ error: error.message });
    }
  }

  async updateSettings(settings, sendResponse) {
    try {
      console.log('Updating settings:', settings);

      if (settings.clientId !== undefined) {
        this.clientId = settings.clientId;
        console.log('Client ID updated to:', this.clientId);
      }
      if (settings.selectedListId !== undefined) this.selectedListId = settings.selectedListId;
      if (settings.autoSync !== undefined) this.settings.autoSync = settings.autoSync;
      if (settings.notificationsEnabled !== undefined) this.settings.notificationsEnabled = settings.notificationsEnabled;

      await this.saveSettings();
      console.log('Settings saved successfully');
      sendResponse({ success: true });
    } catch (error) {
      console.error('Error updating settings:', error);
      sendResponse({ error: error.message });
    }
  }

  async logout(sendResponse) {
    try {
      this.accessToken = null;
      this.tokenExpiry = null;
      await chrome.storage.sync.remove(['accessToken', 'tokenExpiry']);
      sendResponse({ success: true });
    } catch (error) {
      console.error('Error during logout:', error);
      sendResponse({ error: error.message });
    }
  }

  async createTask(assignment) {
    console.log('🔐 Checking authentication...');
    if (!this.accessToken) {
      throw new Error('Not authenticated - no access token available');
    }
    console.log('✅ Access token available');

    const taskData = {
      title: `${assignment.course}: ${assignment.title}`,
      body: {
        content: assignment.description || '',
        contentType: 'text'
      }
    };

    // Add due date if available
    if (assignment.dueDate) {
      taskData.dueDateTime = {
        dateTime: assignment.dueDate + 'T17:00:00.000Z',
        timeZone: 'UTC'
      };
      console.log('📅 Added due date:', taskData.dueDateTime);
    } else {
      console.log('📅 No due date provided');
    }

    const listId = this.selectedListId || 'tasks';
    console.log('📋 Using list ID:', listId);
    console.log('📝 Task data to send:', JSON.stringify(taskData, null, 2));

    const url = `https://graph.microsoft.com/v1.0/me/todo/lists/${listId}/tasks`;
    console.log('🌐 API URL:', url);

    try {
      const response = await fetch(url, {
        method: 'POST',
        headers: {
          'Authorization': `Bearer ${this.accessToken}`,
          'Content-Type': 'application/json'
        },
        body: JSON.stringify(taskData)
      });

      console.log('📡 API Response status:', response.status, response.statusText);

      if (!response.ok) {
        let errorData = {};
        try {
          errorData = await response.json();
          console.log('❌ API Error response:', JSON.stringify(errorData, null, 2));
        } catch (parseError) {
          console.log('❌ Could not parse error response:', parseError);
          const errorText = await response.text();
          console.log('❌ Raw error response:', errorText);
        }

        throw new Error(`Failed to create task: ${response.status} ${response.statusText} - ${errorData.error?.message || errorData.message || 'Unknown error'}`);
      }

      const result = await response.json();
      console.log('✅ Task created successfully:', {
        id: result.id,
        title: result.title,
        status: result.status
      });

      return result;
    } catch (fetchError) {
      console.error('🌐 Network/Fetch error:', fetchError);
      throw new Error(`Network error creating task: ${fetchError.message}`);
    }
  }

  async getExistingTasks() {
    if (!this.accessToken) {
      console.log('No access token, skipping duplicate check');
      return [];
    }

    try {
      const listId = this.selectedListId || 'tasks';
      console.log(`Fetching existing tasks from list: ${listId}`);

      const response = await fetch(`https://graph.microsoft.com/v1.0/me/todo/lists/${listId}/tasks`, {
        headers: {
          'Authorization': `Bearer ${this.accessToken}`,
          'Content-Type': 'application/json'
        }
      });

      if (!response.ok) {
        const errorText = await response.text();
        console.warn(`Failed to fetch existing tasks: ${response.status} - ${errorText}`);
        throw new Error(`HTTP ${response.status}: ${response.statusText}`);
      }

      const data = await response.json();
      const tasks = data.value || [];
      console.log(`Successfully fetched ${tasks.length} existing tasks`);
      return tasks;
    } catch (error) {
      console.error('Error fetching existing tasks:', error);
      throw error; // Re-throw so the caller can handle it
    }
  }

  isSameDueDate(existingDueDateTime, newDueDate) {
    // If both are null/undefined, they're the same
    if (!existingDueDateTime && !newDueDate) return true;

    // If one is null and the other isn't, they're different
    if (!existingDueDateTime || !newDueDate) return false;

    try {
      // Extract date part from existing task (format: "2025-08-27T17:00:00.000Z")
      const existingDate = existingDueDateTime.dateTime.split('T')[0];

      // Compare with new date (format: "2025-08-27")
      return existingDate === newDueDate;
    } catch (error) {
      console.warn('Error comparing due dates:', error);
      return false;
    }
  }
}

// Initialize the extension
const extension = new JupiterEdToDoExtension();
