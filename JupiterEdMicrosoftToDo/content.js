// Content script for JupiterEd to Microsoft To-Do extension
class JupiterEdScraper {
  constructor() {
    this.assignments = [];
    this.currentCourse = "Unknown course";
    this.isJupiterEdPage = this.detectJupiterEdPage();

    // Initialize reporter
    this.reporter = window.ExtensionReporter || new ExtensionReporter();
    this.reporter.log('info', 'init', 'JupiterEdScraper initialized', {
      url: window.location.href,
      isJupiterEdPage: this.isJupiterEdPage
    });

    if (this.isJupiterEdPage) {
      this.init();
    }
  }

  detectJupiterEdPage() {
    // Check if we're on a JupiterEd page
    const hostname = window.location.hostname;
    const isJupiterEd = hostname.includes('jupitered.com') ||
                       hostname.includes('schoology.com') ||
                       document.querySelector('tr.hi, tr.rowhi') !== null;
    if (isJupiterEd) {  
      console.log('JupiterEd page detected');
    }
    return isJupiterEd;
  }

  init() {
    // Add visual indicator that extension is active
    this.addExtensionIndicator();

    // Set up automatic scraping if enabled
    this.checkAutoSync();

    // Listen for manual trigger from popup
    chrome.runtime.onMessage.addListener((message, sender, sendResponse) => {
      if (message.type === 'SCRAPE_ASSIGNMENTS') {
        this.scrapeAssignments()
          .then(assignments => sendResponse({ success: true, assignments }))
          .catch(error => sendResponse({ success: false, error: error.message }));
        return true; // Keep message channel open
      }
    });
  }

  addExtensionIndicator() {
    // Add a small indicator to show the extension is active
    const indicator = document.createElement('div');
    indicator.id = 'jupitered-todo-indicator';
    indicator.innerHTML = '📋 To-Do Sync Active';
    indicator.style.cssText = `
      position: fixed;
      top: 10px;
      right: 10px;
      background: #0078d4;
      color: white;
      padding: 5px 10px;
      border-radius: 5px;
      font-size: 12px;
      z-index: 10000;
      font-family: Arial, sans-serif;
      box-shadow: 0 2px 5px rgba(0,0,0,0.2);
    `;
    document.body.appendChild(indicator);

    // Remove indicator after 3 seconds
    setTimeout(() => {
      if (indicator.parentNode) {
        indicator.parentNode.removeChild(indicator);
      }
    }, 3000);
  }

  async checkAutoSync() {
    try {
      const result = await chrome.storage.sync.get(['autoSync']);
      if (result.autoSync) {
        // Wait a bit for page to fully load
        setTimeout(() => {
          this.scrapeAndUpload();
        }, 2000);
      }
    } catch (error) {
      console.error('Error checking auto sync:', error);
    }
  }

  async scrapeAssignments() {
    console.log('🔍 Starting assignment scraping process...');
    this.assignments = [];
    this.currentCourse = "Unknown course";

    try {
      // Log page information for debugging
      console.log('📄 Page URL:', window.location.href);
      console.log('📄 Page title:', document.title);
      console.log('📄 Domain:', window.location.hostname);

      // Try multiple approaches for different JupiterEd layouts
      let assignments = [];
      let methodUsed = 'none';

      // Method 1: Traditional table layout (tr.hi, tr.rowhi)
      console.log('🔍 Method 1: Searching for table rows (tr.hi, tr.rowhi)...');
      const tableRows = document.querySelectorAll('tr.hi, tr.rowhi');
      console.log(`📊 Found ${tableRows.length} table rows`);

      if (tableRows.length > 0) {
        console.log('✅ Using table row method');
        methodUsed = 'table-rows';
        tableRows.forEach((row, index) => {
          console.log(`🔍 Processing row ${index + 1}/${tableRows.length}`);
          this.processRow(row);
        });
        assignments = [...this.assignments];
        console.log(`📝 Table method extracted ${assignments.length} assignments`);
      }

      // Method 2: Text-based extraction
      if (assignments.length === 0) {
        console.log('🔍 Method 2: Trying text-based extraction...');
        methodUsed = 'text-extraction';
        assignments = this.extractFromText();
        console.log(`📝 Text method extracted ${assignments.length} assignments`);
      }

      // Method 3: Generic assignment containers
      if (assignments.length === 0) {
        console.log('🔍 Method 3: Searching for assignment containers...');
        const containers = document.querySelectorAll('.assignment-row, [data-assignment], .assignment-item');
        console.log(`📊 Found ${containers.length} potential containers`);

        if (containers.length > 0) {
          methodUsed = 'containers';
          containers.forEach((container, index) => {
            console.log(`🔍 Processing container ${index + 1}/${containers.length}`);
            this.processContainer(container);
          });
          assignments = [...this.assignments];
          console.log(`📝 Container method extracted ${assignments.length} assignments`);
        }
      }

      // Method 4: Fallback - look for any elements with assignment-like text
      if (assignments.length === 0) {
        console.log('🔍 Method 4: Fallback search for assignment-like elements...');
        methodUsed = 'fallback';
        assignments = this.fallbackExtraction();
        console.log(`📝 Fallback method extracted ${assignments.length} assignments`);
      }

      if (assignments.length === 0) {
        console.warn('❌ No assignments found using any method');
        console.log('🔍 Page structure analysis:');
        console.log('- Table rows (tr):', document.querySelectorAll('tr').length);
        console.log('- Divs with "assignment" in class:', document.querySelectorAll('div[class*="assignment"]').length);
        console.log('- Elements with "due" text:', document.querySelectorAll('*').length);
        console.log('- Page text length:', document.body.innerText.length);

        throw new Error(`No assignments found on this page. Tried methods: table-rows, text-extraction, containers, fallback. The page layout might not be supported yet.`);
      }

      // Remove duplicates
      const originalCount = assignments.length;
      this.assignments = this.removeDuplicates(assignments);
      const finalCount = this.assignments.length;

      if (originalCount !== finalCount) {
        console.log(`🔄 Removed ${originalCount - finalCount} duplicate assignments`);
      }

      console.log(`✅ Successfully scraped ${this.assignments.length} assignments using method: ${methodUsed}`);
      console.log('📋 Final assignments:', this.assignments);

      return this.assignments;
    } catch (error) {
      console.error('❌ Error scraping assignments:', error);
      console.error('Stack trace:', error.stack);
      throw error;
    }
  }

  processRow(row) {
    try {
      console.log('🔍 Processing row with classes:', row.className);

      if (row.classList.contains('hi')) {
        // Course header row - extract course name from div.big.wrap
        const courseName = row.querySelector('div.big.wrap')?.innerText.trim();
        console.log('📚 Found potential course header:', courseName);

        if (courseName) {
          // Remove grade percentage and letter grade (e.g., "100.0% A+")
          this.currentCourse = courseName.replace(/\s*\d+(\.\d+)?%.*$/g, '').trim();
          console.log('✅ Set current course:', this.currentCourse);
        }
      } else if (row.classList.contains('rowhi')) {
        // Assignment row - extract from specific table structure
        console.log('📝 Processing assignment row...');
        const assignment = this.extractAssignmentData(row);
        if (assignment && assignment.title && assignment.title !== "No title") {
          console.log('✅ Found valid assignment:', assignment);
          this.assignments.push(assignment);
        } else {
          console.log('❌ Assignment extraction failed or invalid data');
        }
      } else {
        console.log('❓ Unknown row type, classes:', row.className);
      }
    } catch (error) {
      console.warn('❌ Error processing row:', error);
      console.warn('Row HTML:', row.outerHTML.substring(0, 200) + '...');
    }
  }

  extractAssignmentData(row) {
    try {
      // Based on your HTML structure:
      // Column 1: Empty padding
      // Column 2: Due date in <div class="printbold" style="min-width:60px">
      // Column 3: Assignment title in <div class="printbold">

      const cells = row.querySelectorAll('td');
      if (cells.length < 3) {
        console.warn('Row does not have expected number of cells:', cells.length);
        return null;
      }

      // Extract due date from 2nd column
      const dueDateElement = cells[1].querySelector('div.printbold');
      const dueRaw = dueDateElement?.innerText.trim() || "No due date";

      // Extract title from 3rd column
      const titleElement = cells[2].querySelector('div.printbold');
      const title = titleElement?.innerText.trim() || "No title";

      if (title === "No title" || !title) {
        return null;
      }

      const dueDate = this.parseDueDate(dueRaw);

      return {
        course: this.currentCourse,
        title: title,
        dueDate: dueDate,
        description: '',
        source: 'JupiterEd',
        scrapedAt: new Date().toISOString()
      };
    } catch (error) {
      console.warn('Error extracting assignment data:', error, row);
      return null;
    }
  }

  parseDueDate(dueRaw) {
    if (!dueRaw || dueRaw === "No due date") return null;

    console.log('Parsing due date:', dueRaw);

    // Clean up the due date string - remove "Due " prefix
    let dueDate = dueRaw.replace(/^Due\s+/i, '').trim();

    try {
      // Handle MM/DD format (like "8/27")
      if (/^\d{1,2}\/\d{1,2}$/.test(dueDate)) {
        const [month, day] = dueDate.split('/').map(Number);
        const today = new Date();
        let year = today.getFullYear();
        const date = new Date(year, month - 1, day);

        // If the date is in the past, assume it's for next year
        if (date < today) {
          date.setFullYear(year + 1);
        }

        const result = this.formatDate(date);
        console.log(`Parsed ${dueRaw} -> ${result}`);
        return result;
      }

      // Handle MM/DD/YYYY format
      if (/^\d{1,2}\/\d{1,2}\/\d{4}$/.test(dueDate)) {
        const [month, day, year] = dueDate.split('/').map(Number);
        const result = this.formatDate(new Date(year, month - 1, day));
        console.log(`Parsed ${dueRaw} -> ${result}`);
        return result;
      }

      // Handle weekday names (Fri, Mon, Tue, etc.)
      const weekdays = ['sun', 'mon', 'tue', 'wed', 'thu', 'fri', 'sat'];
      const dayName = dueDate.toLowerCase().substring(0, 3);
      const dayIndex = weekdays.indexOf(dayName);

      if (dayIndex !== -1) {
        const weekdayDate = this.nextWeekdayDate(dueDate);
        if (weekdayDate) {
          const result = this.formatDate(weekdayDate);
          console.log(`Parsed ${dueRaw} -> ${result}`);
          return result;
        }
      }

      // Try to parse as a regular date string
      const parsedDate = new Date(dueDate);
      if (!isNaN(parsedDate.getTime())) {
        const result = this.formatDate(parsedDate);
        console.log(`Parsed ${dueRaw} -> ${result}`);
        return result;
      }

      console.warn('Could not parse due date:', dueRaw);
      return null;
    } catch (error) {
      console.warn('Error parsing due date:', dueDate, error);
      return null;
    }
  }

  nextWeekdayDate(weekday) {
    const today = new Date();
    const weekdays = ["Sun", "Mon", "Tue", "Wed", "Thu", "Fri", "Sat"];
    const dayIndex = weekdays.findIndex(day =>
      weekday.toLowerCase().startsWith(day.toLowerCase())
    );

    if (dayIndex === -1) return null;

    const date = new Date(today);
    const diff = (dayIndex + 7 - today.getDay()) % 7 || 7;
    date.setDate(today.getDate() + diff);
    return date;
  }

  formatDate(date) {
    const year = date.getFullYear();
    const month = String(date.getMonth() + 1).padStart(2, '0');
    const day = String(date.getDate()).padStart(2, '0');
    return `${year}-${month}-${day}`;
  }

  extractFromText() {
    // Method 2: Text-based extraction for various JupiterEd formats
    const assignments = [];

    try {
      // Look for text patterns that might indicate assignments
      const textContent = document.body.innerText;
      const lines = textContent.split('\n');

      let currentCourse = "Unknown course";

      for (let i = 0; i < lines.length; i++) {
        const line = lines[i].trim();

        // Skip empty lines
        if (!line) continue;

        // Look for course names (usually longer lines with specific patterns)
        if (line.length > 20 && !line.match(/^\d/) && !line.includes('Due')) {
          // Potential course name - clean it up
          const cleanCourse = line.replace(/\s*\d+(\.\d+)?%.*$/g, '').trim();
          if (cleanCourse.length > 5 && cleanCourse.length < 100) {
            currentCourse = cleanCourse;
            console.log('Found potential course:', currentCourse);
            continue;
          }
        }

        // Look for assignment patterns: lines that might contain due dates
        if (line.includes('Due') || line.match(/\d{1,2}\/\d{1,2}/)) {
          // Try to extract assignment info from this line and surrounding lines
          const assignment = this.parseAssignmentLine(line, lines, i, currentCourse);
          if (assignment) {
            assignments.push(assignment);
          }
        }
      }

      console.log(`Text extraction found ${assignments.length} assignments`);
      return assignments;
    } catch (error) {
      console.warn('Error in text extraction:', error);
      return [];
    }
  }

  parseAssignmentLine(line, allLines, lineIndex, course) {
    try {
      // Look for due date patterns
      const dueDateMatch = line.match(/Due\s+(\d{1,2}\/\d{1,2}(?:\/\d{4})?|\w{3})/i) ||
                          line.match(/(\d{1,2}\/\d{1,2}(?:\/\d{4})?)/);

      if (!dueDateMatch) return null;

      const dueRaw = dueDateMatch[1];

      // Look for assignment title in the same line or nearby lines
      let title = '';

      // Try to extract title from the same line
      const titleFromSameLine = line.replace(/Due\s+\d{1,2}\/\d{1,2}(?:\/\d{4})?/i, '').trim();
      if (titleFromSameLine && titleFromSameLine.length > 2) {
        title = titleFromSameLine;
      } else {
        // Look in previous or next lines for the title
        for (let offset = -2; offset <= 2; offset++) {
          const checkIndex = lineIndex + offset;
          if (checkIndex >= 0 && checkIndex < allLines.length && checkIndex !== lineIndex) {
            const checkLine = allLines[checkIndex].trim();
            if (checkLine && checkLine.length > 2 && !checkLine.includes('Due') && !checkLine.match(/^\d{1,2}\/\d{1,2}/)) {
              title = checkLine;
              break;
            }
          }
        }
      }

      if (!title || title.length < 2) return null;

      const dueDate = this.parseDueDate(dueRaw);

      return {
        course: course,
        title: title,
        dueDate: dueDate,
        description: '',
        source: 'JupiterEd (Text)',
        scrapedAt: new Date().toISOString()
      };
    } catch (error) {
      console.warn('Error parsing assignment line:', error);
      return null;
    }
  }

  processContainer(container) {
    // Method 3: Process generic assignment containers
    try {
      console.log('🔍 Processing container:', container.className);

      // Look for common assignment data patterns in the container
      const titleElement = container.querySelector('[class*="title"], [class*="name"], [class*="assignment"]') ||
                          container.querySelector('h1, h2, h3, h4, h5, h6') ||
                          container.querySelector('.printbold, strong, b');

      let title = '';
      let dueRaw = '';

      if (titleElement) {
        title = titleElement.innerText.trim();
        console.log('📝 Found title element:', title);
      }

      // Look for due date in the container text
      const containerText = container.innerText;
      const dueDateMatch = containerText.match(/Due\s+(\d{1,2}\/\d{1,2}(?:\/\d{4})?|\w{3})/i) ||
                          containerText.match(/(\d{1,2}\/\d{1,2}(?:\/\d{4})?)/);

      if (dueDateMatch) {
        dueRaw = dueDateMatch[1];
        console.log('📅 Found due date:', dueRaw);
      }

      // If we couldn't find a good title, use the container text (truncated)
      if (!title || title.length < 2) {
        title = containerText.trim().substring(0, 100);
        console.log('📝 Using container text as title:', title);
      }

      if (title && title.length > 2) {
        const dueDate = this.parseDueDate(dueRaw);

        const assignment = {
          course: this.currentCourse,
          title: title,
          dueDate: dueDate,
          description: '',
          source: 'JupiterEd (Container)',
          scrapedAt: new Date().toISOString()
        };

        console.log('✅ Found assignment from container:', assignment);
        this.assignments.push(assignment);
      } else {
        console.log('❌ Container did not yield valid assignment');
      }
    } catch (error) {
      console.warn('❌ Error processing container:', error);
      console.warn('Container HTML:', container.outerHTML.substring(0, 200) + '...');
    }
  }

  fallbackExtraction() {
    // Method 4: Fallback extraction - look for any text that might be assignments
    console.log('🔍 Starting fallback extraction...');
    const assignments = [];

    try {
      // Look for elements containing date patterns
      const allElements = document.querySelectorAll('*');
      const potentialAssignments = [];

      for (const element of allElements) {
        const text = element.innerText;
        if (!text || text.length < 5) continue;

        // Look for date patterns
        if (text.match(/\d{1,2}\/\d{1,2}/) || text.toLowerCase().includes('due')) {
          potentialAssignments.push({
            element: element,
            text: text.trim(),
            hasDate: true
          });
        }
      }

      console.log(`🔍 Found ${potentialAssignments.length} elements with potential assignment data`);

      // Process potential assignments
      for (const potential of potentialAssignments.slice(0, 20)) { // Limit to first 20 to avoid performance issues
        const assignment = this.extractFromPotentialElement(potential);
        if (assignment) {
          assignments.push(assignment);
        }
      }

      console.log(`📝 Fallback extraction found ${assignments.length} assignments`);
      return assignments;
    } catch (error) {
      console.warn('❌ Error in fallback extraction:', error);
      return [];
    }
  }

  extractFromPotentialElement(potential) {
    try {
      const text = potential.text;

      // Extract due date
      const dueDateMatch = text.match(/Due\s+(\d{1,2}\/\d{1,2}(?:\/\d{4})?|\w{3})/i) ||
                          text.match(/(\d{1,2}\/\d{1,2}(?:\/\d{4})?)/);

      if (!dueDateMatch) return null;

      const dueRaw = dueDateMatch[1];

      // Try to find a title - look for text before or after the due date
      let title = text.replace(/Due\s+\d{1,2}\/\d{1,2}(?:\/\d{4})?/i, '').trim();

      // Clean up the title
      title = title.replace(/^\W+|\W+$/g, '').trim();

      if (!title || title.length < 3) return null;

      // Truncate very long titles
      if (title.length > 100) {
        title = title.substring(0, 100) + '...';
      }

      const dueDate = this.parseDueDate(dueRaw);

      return {
        course: this.currentCourse,
        title: title,
        dueDate: dueDate,
        description: '',
        source: 'JupiterEd (Fallback)',
        scrapedAt: new Date().toISOString()
      };
    } catch (error) {
      console.warn('❌ Error extracting from potential element:', error);
      return null;
    }
  }

  removeDuplicates(assignments) {
    const seen = new Set();
    return assignments.filter(assignment => {
      const key = `${assignment.course}:${assignment.title}:${assignment.dueDate}`;
      if (seen.has(key)) {
        return false;
      }
      seen.add(key);
      return true;
    });
  }

  async scrapeAndUpload() {
    const operation = this.reporter.startOperation('sync', 'scrape and upload assignments');

    try {
      this.reporter.log('info', 'sync', 'Starting automatic scrape and upload');

      const assignments = await this.scrapeAssignments();

      if (assignments.length === 0) {
        this.reporter.log('warning', 'sync', 'No assignments found to upload');
        operation.end(false, 'No assignments found');
        return;
      }

      this.reporter.log('info', 'sync', `Sending ${assignments.length} assignments to background script for upload`);

      // Send to background script for uploading
      chrome.runtime.sendMessage(
        { type: 'UPLOAD_ASSIGNMENTS', assignments },
        (response) => {
          if (chrome.runtime.lastError) {
            this.reporter.log('error', 'sync', 'Error sending message to background script', chrome.runtime.lastError);
            operation.end(false, chrome.runtime.lastError.message);
            return;
          }

          if (response?.error) {
            this.reporter.log('error', 'upload', 'Upload failed', response.error);
            this.showNotification('Upload failed: ' + response.error, 'error');
            operation.end(false, response.error);
          } else if (response) {
            this.reporter.log('success', 'upload', 'Upload completed', response);

            const message = `Successfully uploaded ${response.success} assignments${response.failed > 0 ? `, ${response.failed} failed` : ''}`;
            this.showNotification(message, response.failed > 0 ? 'warning' : 'success');

            operation.end(true, `${response.success} uploaded, ${response.failed} failed, ${response.skipped} skipped`);

            // Print comprehensive report
            setTimeout(() => {
              this.reporter.printReport();
            }, 1000);
          }
        }
      );
    } catch (error) {
      this.reporter.log('error', 'sync', 'Error in scrapeAndUpload', error);
      this.showNotification('Error scraping assignments: ' + error.message, 'error');
      operation.end(false, error.message);
    }
  }

  showNotification(message, type = 'info') {
    const notification = document.createElement('div');
    notification.style.cssText = `
      position: fixed;
      top: 50px;
      right: 10px;
      padding: 10px 15px;
      border-radius: 5px;
      color: white;
      font-family: Arial, sans-serif;
      font-size: 14px;
      z-index: 10001;
      max-width: 300px;
      box-shadow: 0 2px 10px rgba(0,0,0,0.3);
      ${type === 'success' ? 'background: #28a745;' : ''}
      ${type === 'error' ? 'background: #dc3545;' : ''}
      ${type === 'warning' ? 'background: #ffc107; color: black;' : ''}
      ${type === 'info' ? 'background: #17a2b8;' : ''}
    `;
    notification.textContent = message;
    document.body.appendChild(notification);

    setTimeout(() => {
      if (notification.parentNode) {
        notification.parentNode.removeChild(notification);
      }
    }, 5000);
  }
}

// Initialize the scraper
if (document.readyState === 'loading') {
  document.addEventListener('DOMContentLoaded', () => new JupiterEdScraper());
} else {
  new JupiterEdScraper();
}
