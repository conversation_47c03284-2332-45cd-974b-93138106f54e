<!DOCTYPE html>
<html lang="en">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>JupiterEd Extension Test Page</title>
    <style>
        body {
            font-family: Arial, sans-serif;
            max-width: 1200px;
            margin: 0 auto;
            padding: 20px;
            background-color: #f5f5f5;
        }
        .test-section {
            background: white;
            margin: 20px 0;
            padding: 20px;
            border-radius: 8px;
            box-shadow: 0 2px 4px rgba(0,0,0,0.1);
        }
        .test-controls {
            background: #e3f2fd;
            padding: 15px;
            border-radius: 5px;
            margin-bottom: 20px;
        }
        button {
            background: #0078d4;
            color: white;
            border: none;
            padding: 10px 20px;
            border-radius: 5px;
            cursor: pointer;
            margin: 5px;
        }
        button:hover {
            background: #106ebe;
        }
        .log-output {
            background: #1e1e1e;
            color: #d4d4d4;
            padding: 15px;
            border-radius: 5px;
            font-family: 'Courier New', monospace;
            font-size: 12px;
            max-height: 400px;
            overflow-y: auto;
            white-space: pre-wrap;
        }
        .assignment-table {
            width: 100%;
            border-collapse: collapse;
            margin: 10px 0;
        }
        .assignment-table th, .assignment-table td {
            border: 1px solid #ddd;
            padding: 8px;
            text-align: left;
        }
        .assignment-table th {
            background-color: #f2f2f2;
        }
        tr.hi {
            background-color: #e8f4f8;
            font-weight: bold;
        }
        tr.rowhi {
            background-color: #f9f9f9;
        }
        .printbold {
            font-weight: bold;
        }
        .status {
            padding: 10px;
            border-radius: 5px;
            margin: 10px 0;
        }
        .status.success { background: #d4edda; color: #155724; }
        .status.error { background: #f8d7da; color: #721c24; }
        .status.warning { background: #fff3cd; color: #856404; }
        .status.info { background: #d1ecf1; color: #0c5460; }
    </style>
</head>
<body>
    <h1>🧪 JupiterEd Extension Test Page</h1>
    <p>This page simulates a JupiterEd gradebook to test the extension's scraping functionality.</p>

    <div class="test-controls">
        <h3>🎮 Test Controls</h3>
        <button onclick="testScraping()">🔍 Test Scraping</button>
        <button onclick="testReporter()">📊 Test Reporter</button>
        <button onclick="clearLogs()">🧹 Clear Logs</button>
        <button onclick="exportLogs()">📁 Export Logs</button>
        <button onclick="showReport()">📋 Show Report</button>
    </div>

    <div id="status" class="status info">
        Ready to test. Click "Test Scraping" to begin.
    </div>

    <div class="test-section">
        <h3>📚 Simulated JupiterEd Gradebook</h3>
        <table class="assignment-table">
            <tr class="hi">
                <td colspan="3">
                    <div class="big wrap">AP Computer Science A - Period 3 95.2% A</div>
                </td>
            </tr>
            <tr class="rowhi">
                <td style="padding: 5px;"></td>
                <td>
                    <div class="printbold" style="min-width:60px">Due 8/30</div>
                </td>
                <td>
                    <div class="printbold">Programming Assignment 1: Hello World</div>
                </td>
            </tr>
            <tr class="rowhi">
                <td style="padding: 5px;"></td>
                <td>
                    <div class="printbold" style="min-width:60px">Due 9/5</div>
                </td>
                <td>
                    <div class="printbold">Lab 2: Variables and Data Types</div>
                </td>
            </tr>
            <tr class="hi">
                <td colspan="3">
                    <div class="big wrap">English Literature - Period 2 88.7% B+</div>
                </td>
            </tr>
            <tr class="rowhi">
                <td style="padding: 5px;"></td>
                <td>
                    <div class="printbold" style="min-width:60px">Due 9/1</div>
                </td>
                <td>
                    <div class="printbold">Essay: Character Analysis of Hamlet</div>
                </td>
            </tr>
            <tr class="rowhi">
                <td style="padding: 5px;"></td>
                <td>
                    <div class="printbold" style="min-width:60px">Due Fri</div>
                </td>
                <td>
                    <div class="printbold">Reading Assignment: Chapters 5-7</div>
                </td>
            </tr>
            <tr class="hi">
                <td colspan="3">
                    <div class="big wrap">Mathematics - Calculus AB 92.1% A-</div>
                </td>
            </tr>
            <tr class="rowhi">
                <td style="padding: 5px;"></td>
                <td>
                    <div class="printbold" style="min-width:60px">Due 8/28</div>
                </td>
                <td>
                    <div class="printbold">Problem Set 1: Limits and Continuity</div>
                </td>
            </tr>
        </table>
    </div>

    <div class="test-section">
        <h3>📝 Console Output</h3>
        <div id="logOutput" class="log-output">
            Console logs will appear here...
        </div>
    </div>

    <script>
        // Override console.log to capture output
        const originalLog = console.log;
        const originalError = console.error;
        const originalWarn = console.warn;
        
        function logToOutput(level, ...args) {
            const timestamp = new Date().toLocaleTimeString();
            const message = args.map(arg => 
                typeof arg === 'object' ? JSON.stringify(arg, null, 2) : String(arg)
            ).join(' ');
            
            const logOutput = document.getElementById('logOutput');
            logOutput.textContent += `[${timestamp}] ${level.toUpperCase()}: ${message}\n`;
            logOutput.scrollTop = logOutput.scrollHeight;
            
            // Call original console method
            if (level === 'log') originalLog(...args);
            else if (level === 'error') originalError(...args);
            else if (level === 'warn') originalWarn(...args);
        }
        
        console.log = (...args) => logToOutput('log', ...args);
        console.error = (...args) => logToOutput('error', ...args);
        console.warn = (...args) => logToOutput('warn', ...args);

        function updateStatus(message, type = 'info') {
            const status = document.getElementById('status');
            status.textContent = message;
            status.className = `status ${type}`;
        }

        async function testScraping() {
            updateStatus('🔍 Testing scraping functionality...', 'info');
            
            try {
                // Simulate the extension's scraping process
                if (typeof JupiterEdScraper !== 'undefined') {
                    const scraper = new JupiterEdScraper();
                    const assignments = await scraper.scrapeAssignments();
                    
                    updateStatus(`✅ Scraping successful! Found ${assignments.length} assignments`, 'success');
                    console.log('📋 Scraped assignments:', assignments);
                } else {
                    updateStatus('❌ JupiterEdScraper not found. Make sure the extension is loaded.', 'error');
                }
            } catch (error) {
                updateStatus(`❌ Scraping failed: ${error.message}`, 'error');
                console.error('Scraping error:', error);
            }
        }

        function testReporter() {
            updateStatus('📊 Testing reporter functionality...', 'info');
            
            try {
                if (typeof ExtensionReporter !== 'undefined') {
                    const reporter = new ExtensionReporter();
                    
                    // Test various log levels
                    reporter.log('info', 'test', 'Testing info log');
                    reporter.log('success', 'test', 'Testing success log');
                    reporter.log('warning', 'test', 'Testing warning log');
                    reporter.log('error', 'test', 'Testing error log');
                    
                    // Test operation tracking
                    const op = reporter.startOperation('test', 'sample operation');
                    setTimeout(() => {
                        op.end(true, 'Operation completed successfully');
                        updateStatus('✅ Reporter test completed', 'success');
                    }, 1000);
                } else {
                    updateStatus('❌ ExtensionReporter not found. Make sure reporter.js is loaded.', 'error');
                }
            } catch (error) {
                updateStatus(`❌ Reporter test failed: ${error.message}`, 'error');
                console.error('Reporter error:', error);
            }
        }

        function clearLogs() {
            document.getElementById('logOutput').textContent = '';
            updateStatus('🧹 Logs cleared', 'info');
        }

        function exportLogs() {
            try {
                if (window.ExtensionReporter) {
                    window.ExtensionReporter.exportLogs();
                    updateStatus('📁 Logs exported successfully', 'success');
                } else {
                    updateStatus('❌ Reporter not available for export', 'error');
                }
            } catch (error) {
                updateStatus(`❌ Export failed: ${error.message}`, 'error');
            }
        }

        function showReport() {
            try {
                if (window.ExtensionReporter) {
                    window.ExtensionReporter.printReport();
                    updateStatus('📋 Report generated in console', 'success');
                } else {
                    updateStatus('❌ Reporter not available', 'error');
                }
            } catch (error) {
                updateStatus(`❌ Report generation failed: ${error.message}`, 'error');
            }
        }

        // Initialize
        console.log('🧪 Test page loaded and ready');
        console.log('📄 Page URL:', window.location.href);
        console.log('🔍 Looking for extension components...');
        
        // Check if extension components are available
        setTimeout(() => {
            const hasReporter = typeof ExtensionReporter !== 'undefined';
            const hasScraper = typeof JupiterEdScraper !== 'undefined';
            
            console.log('📊 Reporter available:', hasReporter);
            console.log('🔍 Scraper available:', hasScraper);
            
            if (hasReporter && hasScraper) {
                updateStatus('✅ Extension components loaded successfully', 'success');
            } else {
                updateStatus('⚠️ Some extension components missing. Check console for details.', 'warning');
            }
        }, 1000);
    </script>
</body>
</html>
