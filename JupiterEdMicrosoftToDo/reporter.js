// Enhanced reporting system for JupiterEd to Microsoft To-Do extension
class ExtensionReporter {
  constructor() {
    this.sessionId = this.generateSessionId();
    this.startTime = Date.now();
    this.logs = [];
    this.stats = {
      pagesScraped: 0,
      assignmentsFound: 0,
      assignmentsUploaded: 0,
      assignmentsFailed: 0,
      assignmentsSkipped: 0,
      errors: [],
      warnings: []
    };
    
    console.log(`🚀 Extension Reporter initialized - Session: ${this.sessionId}`);
  }

  generateSessionId() {
    return Date.now().toString(36) + Math.random().toString(36).substr(2);
  }

  log(level, category, message, data = null) {
    const timestamp = new Date().toISOString();
    const logEntry = {
      timestamp,
      level,
      category,
      message,
      data,
      sessionId: this.sessionId
    };

    this.logs.push(logEntry);

    // Console output with emojis and formatting
    const emoji = this.getLevelEmoji(level);
    const categoryTag = `[${category.toUpperCase()}]`;
    
    if (data) {
      console.log(`${emoji} ${categoryTag} ${message}`, data);
    } else {
      console.log(`${emoji} ${categoryTag} ${message}`);
    }

    // Update stats based on log entry
    this.updateStats(level, category, message, data);
  }

  getLevelEmoji(level) {
    const emojis = {
      'info': 'ℹ️',
      'success': '✅',
      'warning': '⚠️',
      'error': '❌',
      'debug': '🔍',
      'start': '🚀',
      'end': '🏁'
    };
    return emojis[level] || '📝';
  }

  updateStats(level, category, message, data) {
    if (level === 'error') {
      this.stats.errors.push({ category, message, data, timestamp: new Date().toISOString() });
    }
    
    if (level === 'warning') {
      this.stats.warnings.push({ category, message, data, timestamp: new Date().toISOString() });
    }

    // Update specific stats based on category and message
    if (category === 'scraping') {
      if (message.includes('Starting assignment scraping')) {
        this.stats.pagesScraped++;
      }
      if (message.includes('assignments using method')) {
        const match = message.match(/(\d+) assignments/);
        if (match) {
          this.stats.assignmentsFound += parseInt(match[1]);
        }
      }
    }

    if (category === 'upload') {
      if (data && typeof data === 'object') {
        if (data.success) this.stats.assignmentsUploaded += data.success;
        if (data.failed) this.stats.assignmentsFailed += data.failed;
        if (data.skipped) this.stats.assignmentsSkipped += data.skipped;
      }
    }
  }

  startOperation(category, operation) {
    this.log('start', category, `Starting ${operation}`);
    return {
      category,
      operation,
      startTime: Date.now(),
      end: (success = true, message = '', data = null) => {
        const duration = Date.now() - Date.now();
        const level = success ? 'success' : 'error';
        this.log(level, category, `${operation} ${success ? 'completed' : 'failed'} in ${duration}ms${message ? ': ' + message : ''}`, data);
      }
    };
  }

  generateReport() {
    const duration = Date.now() - this.startTime;
    const report = {
      sessionId: this.sessionId,
      duration: duration,
      durationFormatted: this.formatDuration(duration),
      timestamp: new Date().toISOString(),
      stats: { ...this.stats },
      summary: this.generateSummary(),
      recentLogs: this.logs.slice(-20) // Last 20 log entries
    };

    return report;
  }

  formatDuration(ms) {
    if (ms < 1000) return `${ms}ms`;
    if (ms < 60000) return `${(ms / 1000).toFixed(1)}s`;
    return `${(ms / 60000).toFixed(1)}m`;
  }

  generateSummary() {
    const { stats } = this;
    const totalAssignments = stats.assignmentsFound;
    const successRate = totalAssignments > 0 ? ((stats.assignmentsUploaded / totalAssignments) * 100).toFixed(1) : 0;
    
    return {
      totalAssignmentsFound: stats.assignmentsFound,
      totalAssignmentsUploaded: stats.assignmentsUploaded,
      totalAssignmentsFailed: stats.assignmentsFailed,
      totalAssignmentsSkipped: stats.assignmentsSkipped,
      successRate: `${successRate}%`,
      errorCount: stats.errors.length,
      warningCount: stats.warnings.length,
      pagesScraped: stats.pagesScraped
    };
  }

  printReport() {
    const report = this.generateReport();
    
    console.log('\n' + '='.repeat(60));
    console.log('📊 JUPITERED TO-DO EXTENSION REPORT');
    console.log('='.repeat(60));
    console.log(`🆔 Session ID: ${report.sessionId}`);
    console.log(`⏱️  Duration: ${report.durationFormatted}`);
    console.log(`📅 Timestamp: ${report.timestamp}`);
    console.log('\n📈 STATISTICS:');
    console.log(`   📄 Pages Scraped: ${report.stats.pagesScraped}`);
    console.log(`   📝 Assignments Found: ${report.stats.assignmentsFound}`);
    console.log(`   ✅ Assignments Uploaded: ${report.stats.assignmentsUploaded}`);
    console.log(`   ❌ Assignments Failed: ${report.stats.assignmentsFailed}`);
    console.log(`   ⏭️  Assignments Skipped: ${report.stats.assignmentsSkipped}`);
    console.log(`   📊 Success Rate: ${report.summary.successRate}`);
    console.log(`   ⚠️  Warnings: ${report.stats.warnings.length}`);
    console.log(`   🚨 Errors: ${report.stats.errors.length}`);
    
    if (report.stats.errors.length > 0) {
      console.log('\n🚨 ERRORS:');
      report.stats.errors.forEach((error, index) => {
        console.log(`   ${index + 1}. [${error.category}] ${error.message}`);
        if (error.data) console.log(`      Data:`, error.data);
      });
    }
    
    if (report.stats.warnings.length > 0) {
      console.log('\n⚠️  WARNINGS:');
      report.stats.warnings.forEach((warning, index) => {
        console.log(`   ${index + 1}. [${warning.category}] ${warning.message}`);
      });
    }
    
    console.log('\n💡 RECOMMENDATIONS:');
    this.generateRecommendations(report).forEach((rec, index) => {
      console.log(`   ${index + 1}. ${rec}`);
    });
    
    console.log('='.repeat(60) + '\n');
    
    return report;
  }

  generateRecommendations(report) {
    const recommendations = [];
    
    if (report.stats.assignmentsFound === 0) {
      recommendations.push('No assignments found. Check if you\'re on the correct JupiterEd page.');
      recommendations.push('Try navigating to your assignments or gradebook page.');
    }
    
    if (report.stats.assignmentsFailed > 0) {
      recommendations.push('Some assignments failed to upload. Check your Microsoft To-Do authentication.');
      recommendations.push('Verify your internet connection and try again.');
    }
    
    if (report.stats.errors.length > 0) {
      recommendations.push('Errors occurred during processing. Check the error details above.');
      recommendations.push('Consider reporting persistent errors to the extension developer.');
    }
    
    if (report.stats.assignmentsFound > 0 && report.stats.assignmentsUploaded === 0) {
      recommendations.push('Assignments were found but none were uploaded. Check authentication status.');
      recommendations.push('Verify that your Microsoft To-Do list is accessible.');
    }
    
    if (recommendations.length === 0) {
      recommendations.push('Everything looks good! Your assignments have been successfully synced.');
    }
    
    return recommendations;
  }

  // Export logs for debugging
  exportLogs() {
    const report = this.generateReport();
    const exportData = {
      ...report,
      fullLogs: this.logs,
      userAgent: navigator.userAgent,
      url: window.location.href,
      timestamp: new Date().toISOString()
    };
    
    const blob = new Blob([JSON.stringify(exportData, null, 2)], { type: 'application/json' });
    const url = URL.createObjectURL(blob);
    const a = document.createElement('a');
    a.href = url;
    a.download = `jupitered-todo-logs-${this.sessionId}.json`;
    a.click();
    URL.revokeObjectURL(url);
    
    console.log('📁 Logs exported successfully');
  }
}

// Global reporter instance
window.ExtensionReporter = window.ExtensionReporter || new ExtensionReporter();
