// Debug script to help identify upload issues
// Run this in the browser console on a JupiterEd page

console.log('🔧 Starting upload debug session...');

// Function to test the complete flow
async function debugUploadFlow() {
    console.log('\n=== UPLOAD DEBUG FLOW ===');
    
    try {
        // Step 1: Check if extension components are loaded
        console.log('\n1️⃣ Checking extension components...');
        
        if (typeof JupiterEdScraper === 'undefined') {
            console.error('❌ JupiterEdScraper not found - extension not loaded properly');
            return;
        }
        console.log('✅ JupiterEdScraper found');
        
        if (typeof ExtensionReporter === 'undefined') {
            console.error('❌ ExtensionReporter not found - reporter.js not loaded');
            return;
        }
        console.log('✅ ExtensionReporter found');
        
        // Step 2: Test scraping
        console.log('\n2️⃣ Testing assignment scraping...');
        const scraper = new JupiterEdScraper();
        const assignments = await scraper.scrapeAssignments();
        
        console.log(`✅ Scraping completed: ${assignments.length} assignments found`);
        console.log('📋 Scraped assignments:', assignments);
        
        if (assignments.length === 0) {
            console.warn('⚠️  No assignments found - cannot test upload');
            return;
        }
        
        // Step 3: Test background script communication
        console.log('\n3️⃣ Testing background script communication...');
        
        const authStatus = await new Promise((resolve) => {
            chrome.runtime.sendMessage({ type: 'GET_AUTH_STATUS' }, (response) => {
                if (chrome.runtime.lastError) {
                    resolve({ error: chrome.runtime.lastError.message });
                } else {
                    resolve(response || {});
                }
            });
        });
        
        console.log('🔐 Auth status:', authStatus);
        
        if (authStatus.error) {
            console.error('❌ Background script communication failed:', authStatus.error);
            return;
        }
        
        if (!authStatus.authenticated) {
            console.warn('⚠️  Not authenticated - this will cause upload to fail');
            console.log('💡 Please authenticate through the extension popup first');
        }
        
        if (!authStatus.hasClientId) {
            console.warn('⚠️  No client ID configured - this will cause upload to fail');
            console.log('💡 Please configure client ID in extension settings');
        }
        
        // Step 4: Test upload
        console.log('\n4️⃣ Testing upload process...');
        console.log('📤 Sending assignments to background script...');
        
        const uploadResult = await new Promise((resolve) => {
            chrome.runtime.sendMessage(
                { type: 'UPLOAD_ASSIGNMENTS', assignments },
                (response) => {
                    if (chrome.runtime.lastError) {
                        resolve({ error: chrome.runtime.lastError.message });
                    } else {
                        resolve(response || {});
                    }
                }
            );
        });
        
        console.log('📊 Upload result:', uploadResult);
        
        // Step 5: Analyze results
        console.log('\n5️⃣ Analyzing results...');
        
        if (uploadResult.error) {
            console.error('❌ Upload failed with error:', uploadResult.error);
            
            // Common error analysis
            if (uploadResult.error.includes('Client ID not configured')) {
                console.log('💡 Solution: Configure your Azure App Client ID in extension settings');
            } else if (uploadResult.error.includes('Authentication failed')) {
                console.log('💡 Solution: Re-authenticate through the extension popup');
            } else if (uploadResult.error.includes('HTTP 401')) {
                console.log('💡 Solution: Token expired, try re-authenticating');
            } else if (uploadResult.error.includes('HTTP 403')) {
                console.log('💡 Solution: Check Microsoft To-Do permissions');
            }
        } else {
            console.log('📈 Upload statistics:');
            console.log(`   ✅ Success: ${uploadResult.success || 0}`);
            console.log(`   ❌ Failed: ${uploadResult.failed || 0}`);
            console.log(`   ⏭️  Skipped: ${uploadResult.skipped || 0}`);
            
            if (uploadResult.errors && uploadResult.errors.length > 0) {
                console.log('🚨 Errors:');
                uploadResult.errors.forEach((error, index) => {
                    console.log(`   ${index + 1}. ${error}`);
                });
            }
            
            // Analyze why success might be 0
            if ((uploadResult.success || 0) === 0) {
                console.log('\n🔍 Analyzing why no tasks were uploaded:');
                
                if ((uploadResult.skipped || 0) > 0) {
                    console.log('❓ All tasks were skipped - likely marked as duplicates');
                    console.log('💡 This could mean:');
                    console.log('   - Tasks already exist in Microsoft To-Do');
                    console.log('   - Duplicate detection is too aggressive');
                    console.log('   - Date comparison is not working correctly');
                }
                
                if ((uploadResult.failed || 0) > 0) {
                    console.log('❓ All tasks failed to upload');
                    console.log('💡 Check the error messages above for specific issues');
                }
                
                if ((uploadResult.failed || 0) === 0 && (uploadResult.skipped || 0) === 0) {
                    console.log('❓ No tasks were processed at all');
                    console.log('💡 This suggests an issue in the upload loop logic');
                }
            }
        }
        
        console.log('\n🏁 Debug session completed');
        
    } catch (error) {
        console.error('❌ Debug session failed:', error);
        console.error('Stack trace:', error.stack);
    }
}

// Function to test just the scraping
async function debugScraping() {
    console.log('\n=== SCRAPING DEBUG ===');
    
    try {
        if (typeof JupiterEdScraper === 'undefined') {
            console.error('❌ JupiterEdScraper not found');
            return;
        }
        
        const scraper = new JupiterEdScraper();
        console.log('🔍 Page detection:', scraper.isJupiterEdPage);
        console.log('📄 Current URL:', window.location.href);
        console.log('📄 Page title:', document.title);
        
        const assignments = await scraper.scrapeAssignments();
        console.log(`📋 Found ${assignments.length} assignments:`, assignments);
        
        return assignments;
    } catch (error) {
        console.error('❌ Scraping debug failed:', error);
    }
}

// Function to test authentication
async function debugAuth() {
    console.log('\n=== AUTHENTICATION DEBUG ===');
    
    try {
        const authStatus = await new Promise((resolve) => {
            chrome.runtime.sendMessage({ type: 'GET_AUTH_STATUS' }, (response) => {
                resolve(response || {});
            });
        });
        
        console.log('🔐 Authentication status:', authStatus);
        
        if (!authStatus.authenticated) {
            console.log('🔑 Attempting authentication...');
            const authResult = await new Promise((resolve) => {
                chrome.runtime.sendMessage({ type: 'AUTHENTICATE' }, (response) => {
                    resolve(response || {});
                });
            });
            console.log('🔐 Authentication result:', authResult);
        }
        
        return authStatus;
    } catch (error) {
        console.error('❌ Auth debug failed:', error);
    }
}

// Export functions to global scope
window.debugUploadFlow = debugUploadFlow;
window.debugScraping = debugScraping;
window.debugAuth = debugAuth;

console.log('🔧 Debug functions loaded. Available commands:');
console.log('   debugUploadFlow() - Test complete scraping and upload flow');
console.log('   debugScraping() - Test just the scraping functionality');
console.log('   debugAuth() - Test authentication status');
console.log('\n💡 Run debugUploadFlow() to start comprehensive debugging');
